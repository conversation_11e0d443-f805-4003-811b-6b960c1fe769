'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { Link, Shield, Unlink } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// Social account interface
interface SocialAccount {
  id: string
  provider: string
  name: string
  avatar?: string
  account_id: string
  created_at: string
}

// Social providers configuration
const socialProviders = [
  {
    id: 'google',
    name: '<PERSON>',
    icon: '🔍',
    color: 'bg-red-500',
  },
  {
    id: 'github',
    name: 'GitH<PERSON>',
    icon: '🐙',
    color: 'bg-gray-800',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '📘',
    color: 'bg-blue-600',
  },
] as const

export default function SocialLoginSection(): JSX.Element {
  // Fetch user's social accounts
  const { data: socialAccounts, refetch: refetchSocialAccounts } = useQuery<ApiResponse<SocialAccount[]>>({
    queryKey: ['socialAccounts'],
    queryFn: () => queryFetchHelper('/profile/social-accounts'),
  })

  // Social account disconnect mutation
  const { isPending: isDisconnecting, mutate: disconnectSocialAccount } = useMutation<ApiResponse, Error, string>({
    mutationFn: async (accountId: string) => {
      return queryFetchHelper(`/profile/social-accounts/${accountId}`, {
        method: 'DELETE',
      })
    },
    onSuccess: (data) => {
      toast.success(data.message || 'Tài khoản đã được ngắt kết nối')
      refetchSocialAccounts()
    },
    onError: (error) => {
      toast.error(error.message || 'Ngắt kết nối thất bại')
    },
  })

  const handleDisconnectSocialAccount = (accountId: string): void => {
    disconnectSocialAccount(accountId)
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <Shield className="h-5 w-5" />
          Quản lý đăng nhập xã hội
        </h2>
        <p className="text-muted-foreground text-sm">Kết nối hoặc ngắt kết nối các tài khoản mạng xã hội của bạn</p>
      </div>
      <div>
        <div className="space-y-4">
          {socialProviders.map((provider) => {
            const connectedAccount = socialAccounts?.data?.find((account) => account.provider === provider.id)

            return (
              <div
                key={provider.id}
                className="flex items-center justify-between rounded-lg border p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      'flex h-10 w-10 items-center justify-center rounded-full text-white',
                      provider.color
                    )}>
                    <span className="text-lg">{provider.icon}</span>
                  </div>
                  <div>
                    <h4 className="font-medium">{provider.name}</h4>
                    {connectedAccount ? (
                      <p className="text-muted-foreground text-sm">Đã kết nối với {connectedAccount.name}</p>
                    ) : (
                      <p className="text-muted-foreground text-sm">Chưa kết nối</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {connectedAccount ? (
                    <>
                      <Badge
                        variant="secondary"
                        className="flex items-center gap-1">
                        <Link className="h-3 w-3" />
                        Đã kết nối
                      </Badge>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={isDisconnecting}>
                            <Unlink className="mr-1 h-4 w-4" />
                            Ngắt kết nối
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xác nhận ngắt kết nối</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn có chắc chắn muốn ngắt kết nối tài khoản {provider.name} này không? Bạn sẽ không thể
                              đăng nhập bằng {provider.name} nữa.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDisconnectSocialAccount(connectedAccount.id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                              Ngắt kết nối
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm">
                      <Link className="mr-1 h-4 w-4" />
                      Kết nối
                    </Button>
                  )}
                </div>
              </div>
            )
          })}

          {socialAccounts?.data?.length === 0 && (
            <div className="text-muted-foreground py-8 text-center">
              <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
              <p>Chưa có tài khoản mạng xã hội nào được kết nối</p>
              <p className="text-sm">Kết nối tài khoản để đăng nhập dễ dàng hơn</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

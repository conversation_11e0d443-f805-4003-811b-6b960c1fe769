'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { Key } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormItemPasswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Button } from '@/components/ui/button'
import { Form, FormDescription, FormField } from '@/components/ui/form'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// Password change form schema
const passwordFormSchema = z
  .object({
    current_password: z.string().min(1, 'Mật khẩu hiện tại là bắt buộc'),
    new_password: z.string().min(8, 'Mật khẩu mới phải có ít nhất 8 ký tự'),
    confirm_password: z.string().min(1, '<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc'),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: '<PERSON>ật khẩu xác nhận không khớp',
    path: ['confirm_password'],
  })

type PasswordFormValues = z.infer<typeof passwordFormSchema>

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

export default function PasswordChangeSection() {
  // Password change form
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
  })

  // Password change mutation
  const { isPending: isChangingPassword, mutate: changePassword } = useMutation<ApiResponse, Error, PasswordFormValues>(
    {
      mutationFn: async (data: PasswordFormValues) => {
        return queryFetchHelper('/authentication/password', {
          method: 'PATCH',
          body: JSON.stringify({
            current_password: data.current_password,
            new_password: data.new_password,
          }),
        })
      },
      onSuccess: (data) => {
        toast.success(data.message || 'Mật khẩu đã được cập nhật thành công')
        passwordForm.reset()
      },
      onError: (error) => {
        toast.error(error.message || 'Cập nhật mật khẩu thất bại')
      },
    }
  )

  const onPasswordSubmit = (data: PasswordFormValues): void => {
    changePassword(data)
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <Key className="h-5 w-5" />
          Thay đổi mật khẩu
        </h2>
        <p className="text-muted-foreground text-sm">Cập nhật mật khẩu của bạn để bảo mật tài khoản</p>
      </div>
      <div>
        <Form {...passwordForm}>
          <form
            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
            className="space-y-4">
            <FormField
              control={passwordForm.control}
              name="current_password"
              render={({ field }) => (
                <FormItemPasswordGenerator<PasswordFormValues>
                  field={field}
                  label="Mật khẩu hiện tại"
                  showGenerator={false}
                />
              )}
            />
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={passwordForm.control}
                name="new_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu mới</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showNewPassword ? 'text' : 'password'}
                          placeholder="Nhập mật khẩu mới"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}>
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription>Mật khẩu phải có ít nhất 8 ký tự</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="confirm_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Xác nhận mật khẩu mới</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Nhập lại mật khẩu mới"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}>
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription>&nbsp;</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button
              type="submit"
              disabled={isChangingPassword}>
              {isChangingPassword ? 'Đang cập nhật...' : 'Cập nhật mật khẩu'}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  )
}

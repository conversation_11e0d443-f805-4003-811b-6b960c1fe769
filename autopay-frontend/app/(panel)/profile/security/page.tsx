'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Eye, EyeOff, Key, Link, Shield, Unlink } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// Password change form schema
const passwordFormSchema = z
  .object({
    current_password: z.string().min(1, 'Mật khẩu hiện tại là bắt buộc'),
    new_password: z.string().min(8, 'Mật khẩu mới phải có ít nhất 8 ký tự'),
    confirm_password: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc'),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: 'Mật khẩu xác nhận không khớp',
    path: ['confirm_password'],
  })

type PasswordFormValues = z.infer<typeof passwordFormSchema>

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// Social account interface
interface SocialAccount {
  id: string
  provider: string
  name: string
  avatar?: string
  account_id: string
  created_at: string
}

// Social providers configuration
const socialProviders = [
  {
    id: 'google',
    name: 'Google',
    icon: '🔍',
    color: 'bg-red-500',
  },
  {
    id: 'github',
    name: 'GitHub',
    icon: '🐙',
    color: 'bg-gray-800',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '📘',
    color: 'bg-blue-600',
  },
] as const

export default function SecurityPage() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Password change form
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
  })

  // Fetch user's social accounts
  const { data: socialAccounts, refetch: refetchSocialAccounts } = useQuery<ApiResponse<SocialAccount[]>>({
    queryKey: ['socialAccounts'],
    queryFn: () => queryFetchHelper('/profile/social-accounts'),
  })

  // Password change mutation
  const { isPending: isChangingPassword, mutate: changePassword } = useMutation<ApiResponse, Error, PasswordFormValues>(
    {
      mutationFn: async (data: PasswordFormValues) => {
        return queryFetchHelper('/authentication/password', {
          method: 'PATCH',
          body: JSON.stringify({
            current_password: data.current_password,
            new_password: data.new_password,
          }),
        })
      },
      onSuccess: (data) => {
        toast.success(data.message || 'Mật khẩu đã được cập nhật thành công')
        passwordForm.reset()
      },
      onError: (error) => {
        toast.error(error.message || 'Cập nhật mật khẩu thất bại')
      },
    }
  )

  // Social account disconnect mutation
  const { isPending: isDisconnecting, mutate: disconnectSocialAccount } = useMutation<ApiResponse, Error, string>({
    mutationFn: async (accountId: string) => {
      return queryFetchHelper(`/profile/social-accounts/${accountId}`, {
        method: 'DELETE',
      })
    },
    onSuccess: (data) => {
      toast.success(data.message || 'Tài khoản đã được ngắt kết nối')
      refetchSocialAccounts()
    },
    onError: (error) => {
      toast.error(error.message || 'Ngắt kết nối thất bại')
    },
  })

  const onPasswordSubmit = (data: PasswordFormValues): void => {
    changePassword(data)
  }

  const handleDisconnectSocialAccount = (accountId: string): void => {
    disconnectSocialAccount(accountId)
  }

  return (
    <div className="space-y-6">
      {/* Password Change Section */}
      <div className="space-y-4">
        <div>
          <h2 className="flex items-center gap-2 text-lg font-semibold">
            <Key className="h-5 w-5" />
            Thay đổi mật khẩu
          </h2>
          <p className="text-muted-foreground text-sm">Cập nhật mật khẩu của bạn để bảo mật tài khoản</p>
        </div>
        <div>
          <Form {...passwordForm}>
            <form
              onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
              className="space-y-4">
              <FormField
                control={passwordForm.control}
                name="current_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu hiện tại</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showCurrentPassword ? 'text' : 'password'}
                          placeholder="Nhập mật khẩu hiện tại"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}>
                          {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="new_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu mới</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showNewPassword ? 'text' : 'password'}
                          placeholder="Nhập mật khẩu mới"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}>
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription>Mật khẩu phải có ít nhất 8 ký tự</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="confirm_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Xác nhận mật khẩu mới</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Nhập lại mật khẩu mới"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}>
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                disabled={isChangingPassword}>
                {isChangingPassword ? 'Đang cập nhật...' : 'Cập nhật mật khẩu'}
              </Button>
            </form>
          </Form>
        </div>
      </div>

      {/* Social Login Management Section */}
      <div className="space-y-4">
        <div>
          <h2 className="flex items-center gap-2 text-lg font-semibold">
            <Shield className="h-5 w-5" />
            Quản lý đăng nhập xã hội
          </h2>
          <p className="text-muted-foreground text-sm">Kết nối hoặc ngắt kết nối các tài khoản mạng xã hội của bạn</p>
        </div>
        <div>
          <div className="space-y-4">
            {socialProviders.map((provider) => {
              const connectedAccount = socialAccounts?.data?.find((account) => account.provider === provider.id)

              return (
                <div
                  key={provider.id}
                  className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        'flex h-10 w-10 items-center justify-center rounded-full text-white',
                        provider.color
                      )}>
                      <span className="text-lg">{provider.icon}</span>
                    </div>
                    <div>
                      <h4 className="font-medium">{provider.name}</h4>
                      {connectedAccount ? (
                        <p className="text-muted-foreground text-sm">Đã kết nối với {connectedAccount.name}</p>
                      ) : (
                        <p className="text-muted-foreground text-sm">Chưa kết nối</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {connectedAccount ? (
                      <>
                        <Badge
                          variant="secondary"
                          className="flex items-center gap-1">
                          <Link className="h-3 w-3" />
                          Đã kết nối
                        </Badge>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              disabled={isDisconnecting}>
                              <Unlink className="mr-1 h-4 w-4" />
                              Ngắt kết nối
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Xác nhận ngắt kết nối</AlertDialogTitle>
                              <AlertDialogDescription>
                                Bạn có chắc chắn muốn ngắt kết nối tài khoản {provider.name} này không? Bạn sẽ không thể
                                đăng nhập bằng {provider.name} nữa.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Hủy</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDisconnectSocialAccount(connectedAccount.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                                Ngắt kết nối
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm">
                        <Link className="mr-1 h-4 w-4" />
                        Kết nối
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}

            {socialAccounts?.data?.length === 0 && (
              <div className="text-muted-foreground py-8 text-center">
                <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>Chưa có tài khoản mạng xã hội nào được kết nối</p>
                <p className="text-sm">Kết nối tài khoản để đăng nhập dễ dàng hơn</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator />

      {/* Security Tips */}
      <div className="space-y-4">
        <div>
          <h2 className="flex items-center gap-2 text-lg font-semibold">
            <Shield className="h-5 w-5" />
            Mẹo bảo mật
          </h2>
        </div>
        <div>
          <div className="text-muted-foreground space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <div className="bg-primary mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full" />
              <p>Sử dụng mật khẩu mạnh với ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="bg-primary mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full" />
              <p>Không sử dụng cùng một mật khẩu cho nhiều tài khoản khác nhau</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="bg-primary mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full" />
              <p>Kết nối tài khoản mạng xã hội để có thêm phương thức đăng nhập an toàn</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="bg-primary mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full" />
              <p>Thường xuyên thay đổi mật khẩu để đảm bảo an toàn tài khoản</p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
